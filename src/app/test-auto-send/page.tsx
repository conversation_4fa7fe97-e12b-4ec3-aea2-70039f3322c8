"use client";

import { useState } from "react";
import { SpeechRecognitionInput } from "@/components/interview/speech-recognition-input";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function TestAutoSendPage() {
  const [messageInput1, setMessageInput1] = useState("");
  const [messageInput2, setMessageInput2] = useState("");
  const [messages, setMessages] = useState<string[]>([]);
  const [timerPosition, setTimerPosition] = useState<
    "top-right" | "bottom-right"
  >("top-right");

  const handleSendMessage = (inputType: string, message: string) => {
    if (message.trim()) {
      const timestamp = new Date().toLocaleTimeString();
      console.log("[Test] Message sent:", message, "at", timestamp);
      setMessages((prev) => [
        ...prev,
        `[${inputType}] ${message} (sent at ${timestamp})`,
      ]);

      // Clear the appropriate input
      if (inputType === "Top Input") {
        setMessageInput1("");
      } else {
        setMessageInput2("");
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              ✨ Enhanced Auto-Send Timer Demo
            </CardTitle>
            <p className="text-muted-foreground text-center text-lg">
              Experience the redesigned auto-send timer with modern UI and
              animations
            </p>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg p-4 border border-primary/20">
                <h4 className="font-semibold text-primary mb-2">
                  🎨 Modern Design
                </h4>
                <p className="text-sm text-muted-foreground">
                  Glassmorphism effects, gradients, and smooth animations
                </p>
              </div>
              <div className="bg-gradient-to-br from-secondary/10 to-secondary/5 rounded-lg p-4 border border-secondary/20">
                <h4 className="font-semibold text-secondary-foreground mb-2">
                  ⚡ Smart States
                </h4>
                <p className="text-sm text-muted-foreground">
                  Urgent state (≤3 secs) with red colors and bounce animations
                </p>
              </div>
              <div className="bg-gradient-to-br from-accent/10 to-accent/5 rounded-lg p-4 border border-accent/20">
                <h4 className="font-semibold text-accent-foreground mb-2">
                  🎯 Positioning
                </h4>
                <p className="text-sm text-muted-foreground">
                  Flexible positioning (top-right or bottom-right corner)
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Messages Display */}
            <div className="space-y-2">
              <h3 className="font-semibold">Sent Messages:</h3>
              <div className="min-h-[200px] max-h-[300px] overflow-y-auto bg-muted/30 rounded-lg p-4 space-y-2">
                {messages.length === 0 ? (
                  <p className="text-muted-foreground text-center">
                    No messages sent yet. Try typing something below!
                  </p>
                ) : (
                  messages.map((message, index) => (
                    <div
                      key={index}
                      className="bg-primary/10 rounded-lg p-3 border border-primary/20"
                    >
                      <p className="text-sm font-medium">
                        Message #{index + 1}
                      </p>
                      <p className="mt-1">{message}</p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Sent at: {new Date().toLocaleTimeString()}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Timer Position Toggle */}
            <div className="flex items-center justify-center gap-4 mb-6">
              <span className="text-sm font-medium">Timer Position:</span>
              <div className="flex gap-2">
                <Button
                  variant={
                    timerPosition === "top-right" ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setTimerPosition("top-right")}
                >
                  Top Right
                </Button>
                <Button
                  variant={
                    timerPosition === "bottom-right" ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setTimerPosition("bottom-right")}
                >
                  Bottom Right
                </Button>
              </div>
              <Badge variant="secondary" className="ml-2">
                Current: {timerPosition}
              </Badge>
            </div>

            {/* Test Inputs */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* First Input */}
              <div className="space-y-2">
                <h3 className="font-semibold flex items-center gap-2">
                  🎯 Test Input #1
                  <Badge variant="outline">Timer: {timerPosition}</Badge>
                </h3>
                <SpeechRecognitionInput
                  placeholder="Type your message and wait 5 seconds..."
                  value={messageInput1}
                  onChange={setMessageInput1}
                  onSend={() => handleSendMessage("Input #1", messageInput1)}
                  disabled={false}
                  rows={3}
                  className="w-full"
                  timerPosition={timerPosition}
                />
              </div>

              {/* Second Input */}
              <div className="space-y-2">
                <h3 className="font-semibold flex items-center gap-2">
                  🎯 Test Input #2
                  <Badge variant="outline">Timer: {timerPosition}</Badge>
                </h3>
                <SpeechRecognitionInput
                  placeholder="Another input to test timer behavior..."
                  value={messageInput2}
                  onChange={setMessageInput2}
                  onSend={() => handleSendMessage("Input #2", messageInput2)}
                  disabled={false}
                  rows={3}
                  className="w-full"
                  timerPosition={timerPosition}
                />
              </div>
            </div>

            {/* Enhanced Instructions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-4">
                <h4 className="font-semibold text-primary mb-3">
                  ✨ New Features to Try:
                </h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>
                      <strong>Position Toggle:</strong> Switch between top-right
                      and bottom-right
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>
                      <strong>Urgent State:</strong> Watch the timer turn red
                      when ≤3 seconds
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>
                      <strong>Hover Effects:</strong> Hover over the timer to
                      see the cancel button
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>
                      <strong>Progress Bar:</strong> Visual countdown with
                      shimmer animation
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="text-primary">•</span>
                    <span>
                      <strong>Glassmorphism:</strong> Modern backdrop blur and
                      transparency
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-gradient-to-br from-secondary/10 to-secondary/5 border border-secondary/20 rounded-lg p-4">
                <h4 className="font-semibold text-secondary-foreground mb-3">
                  🎯 Testing Steps:
                </h4>
                <ol className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      1
                    </Badge>
                    <span>Type a message in either input field</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      2
                    </Badge>
                    <span>
                      Stop typing and wait 5 seconds for silent period
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      3
                    </Badge>
                    <span>Watch the enhanced timer appear with animations</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      4
                    </Badge>
                    <span>
                      Observe the urgent state when countdown ≤3 seconds
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      5
                    </Badge>
                    <span>
                      Try canceling by typing or clicking the × button
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Badge
                      variant="outline"
                      className="text-xs min-w-[20px] h-5"
                    >
                      6
                    </Badge>
                    <span>Test voice input with the microphone button</span>
                  </li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
