"use client";

import { Clock, Send, X, Zap } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface AutoSendTimerProps {
  isActive: boolean;
  seconds: number;
  onCancel?: () => void;
  className?: string;
  position?: "top-right" | "bottom-right";
}

export function AutoSendTimer({
  isActive,
  seconds,
  onCancel,
  className,
  position = "top-right",
}: AutoSendTimerProps) {
  if (!isActive || seconds <= 0) {
    return null;
  }

  const progressPercentage = (seconds / 10) * 100;
  const isUrgent = seconds <= 3;

  const positionClasses = {
    "top-right": "top-4 right-4 slide-in-from-top-2",
    "bottom-right": "bottom-4 right-4 slide-in-from-bottom-2",
  };

  return (
    <div
      className={cn(
        "fixed z-[9999] group",
        "animate-in duration-500 ease-out",
        positionClasses[position],
        className,
      )}
    >
      {/* Main timer container */}
      <div
        className={cn(
          "relative bg-gradient-to-br from-background/95 to-background/90 backdrop-blur-md",
          "border border-primary/20 rounded-2xl shadow-2xl",
          "transition-all duration-300 ease-out",
          "hover:shadow-primary/20 hover:border-primary/40",
          isUrgent &&
            "animate-pulse border-destructive/40 shadow-destructive/20",
        )}
      >
        {/* Animated background gradient */}
        <div
          className={cn(
            "absolute inset-0 rounded-2xl opacity-30",
            "bg-gradient-to-br from-primary/10 via-transparent to-primary/5",
            "animate-gradient-shift",
          )}
        />

        {/* Cancel button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className={cn(
            "absolute -top-2 -right-2 h-7 w-7 rounded-full z-10",
            "bg-background/90 backdrop-blur-sm border border-border/50 shadow-lg",
            "hover:bg-destructive hover:text-destructive-foreground hover:border-destructive/50",
            "transition-all duration-200 ease-out",
            "opacity-0 group-hover:opacity-100",
          )}
        >
          <X className="h-3.5 w-3.5" />
        </Button>

        {/* Content */}
        <div className="relative p-4 space-y-3">
          {/* Header with icon and title */}
          <div className="flex items-center gap-3">
            {/* Animated send icon */}
            <div className="relative">
              <div
                className={cn(
                  "absolute inset-0 rounded-full",
                  "bg-gradient-to-r from-primary/30 to-primary/10",
                  "animate-ping",
                  isUrgent && "animate-pulse bg-destructive/30",
                )}
              />
              <div
                className={cn(
                  "relative p-2.5 rounded-full",
                  "bg-gradient-to-br from-primary/20 to-primary/10",
                  "border border-primary/30",
                  isUrgent &&
                    "bg-gradient-to-br from-destructive/20 to-destructive/10 border-destructive/30",
                )}
              >
                {isUrgent ? (
                  <Zap className="h-4 w-4 text-destructive animate-bounce" />
                ) : (
                  <Send className="h-4 w-4 text-primary" />
                )}
              </div>
            </div>

            {/* Timer display */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-1.5 mb-1">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs font-medium text-muted-foreground">
                  Auto-sending in
                </span>
              </div>
              <div className="flex items-baseline gap-1">
                <span
                  className={cn(
                    "text-2xl font-bold tabular-nums transition-colors duration-200",
                    isUrgent ? "text-destructive" : "text-primary",
                  )}
                >
                  {seconds}
                </span>
                <span className="text-sm text-muted-foreground">
                  {seconds === 1 ? "sec" : "secs"}
                </span>
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="space-y-2">
            <div className="relative h-2 bg-muted/50 rounded-full overflow-hidden">
              {/* Background track */}
              <div className="absolute inset-0 bg-gradient-to-r from-muted/30 to-muted/60 rounded-full" />

              {/* Progress fill */}
              <div
                className={cn(
                  "absolute left-0 top-0 h-full rounded-full transition-all duration-1000 ease-linear",
                  "bg-gradient-to-r",
                  isUrgent
                    ? "from-destructive to-destructive/80"
                    : "from-primary to-primary/80",
                )}
                style={{ width: `${100 - progressPercentage}%` }}
              />

              {/* Shimmer effect */}
              <div
                className={cn(
                  "absolute inset-0 rounded-full",
                  "bg-gradient-to-r from-transparent via-white/20 to-transparent",
                  "animate-shimmer",
                )}
              />
            </div>

            {/* Action hint */}
            <p className="text-xs text-muted-foreground text-center leading-tight">
              Keep typing to reset • Click{" "}
              <span className="inline-flex items-center">
                <X className="h-2.5 w-2.5 mx-0.5" />
              </span>{" "}
              to cancel
            </p>
          </div>
        </div>

        {/* Glow effect for urgent state */}
        {isUrgent && (
          <div className="absolute inset-0 rounded-2xl bg-destructive/10 animate-pulse pointer-events-none" />
        )}
      </div>
    </div>
  );
}
